from supabase import create_client, Client
from backend.app.config.env import supabase_settings
import asyncio

url = supabase_settings.supabase_url
anon_key = supabase_settings.supabase_anon_key
supa: Client = create_client(url, anon_key )


async def sign_in():

  res = supa.auth.sign_in_with_password({"email":"<EMAIL>", "password": "usertest123"})
  return res.get("access_token")

if __name__ == "__main__":
    asyncio.run(sign_in())
