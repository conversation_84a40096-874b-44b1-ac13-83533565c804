import sys
import asyncio
from pathlib import Path
from loguru import logger


from backend.app.services.supabase_auth_service import get_auth_service



async def test_basic_auth():
    """测试基本认证功能"""
    print("\n=== 🧪 基本认证功能测试 ===")

    auth_service = get_auth_service()

    # 测试用户信息
    test_email = "<EMAIL>"
    test_password = "usertest123"

    print(f"📧 测试邮箱: {test_email}")
    print(f"🔑 测试密码: {test_password}")

    # 2. 测试用户登录
    print("\n2️⃣ 测试用户登录...")
    session_data = None
    try:
        signin_result = await auth_service.sign_in_with_email(
            email=test_email,
            password=test_password
        )

        print(f"   登录结果: {'✅ 成功' if signin_result['success'] else '❌ 失败'}")
        print(f"   消息: {signin_result['message']}")

        if signin_result['success']:
            session_data = signin_result
            print(f"   用户ID: {signin_result['user']['id']}")
            print(f"   邮箱: {signin_result['user']['email']}")
            print(f"   访问令牌: {signin_result['access_token'][:20]}...")

    except Exception as e:
        print(f"   ❌ 登录异常: {e}")


async def main():
    """主函数"""
    print("🚀 开始 Supabase Auth Service 测试")
    print("=" * 50)

    # 2. 基础功能测试
    await test_basic_auth()


    print("\n" + "=" * 50)
    print("🎉 所有测试完成!")


if __name__ == "__main__":
    asyncio.run(main())
