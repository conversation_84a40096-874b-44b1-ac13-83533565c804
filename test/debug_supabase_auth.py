"""
Supabase Auth 调试测试

专门用于调试 Supabase 认证服务的连接和配置问题
"""

import sys
import asyncio
import requests
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from loguru import logger
from backend.app.config.env import supabase_settings
from backend.app.services.supabase_client_service import get_supabase_client


def test_direct_api_calls():
    """直接测试 API 调用"""
    print("\n=== 🔍 直接 API 调试测试 ===")
    
    base_url = supabase_settings.supabase_url
    anon_key = supabase_settings.supabase_anon_key
    service_role_key = supabase_settings.supabase_service_role_key
    
    print(f"Base URL: {base_url}")
    print(f"Anon Key: {anon_key[:20]}..." if anon_key else "Anon Key: None")
    print(f"Service Role Key: {service_role_key[:20]}..." if service_role_key else "Service Role Key: None")
    
    # 测试不同的端点
    endpoints = [
        "/auth/v1/health",
        "/auth/v1/settings", 
        "/rest/v1/",
        "/storage/v1/bucket"
    ]
    
    headers_anon = {
        "apikey": anon_key,
        "Authorization": f"Bearer {anon_key}",
        "Content-Type": "application/json"
    }
    
    headers_service = {
        "apikey": service_role_key,
        "Authorization": f"Bearer {service_role_key}",
        "Content-Type": "application/json"
    }
    
    for endpoint in endpoints:
        print(f"\n📍 测试端点: {endpoint}")
        url = f"{base_url}{endpoint}"
        
        # 使用匿名密钥测试
        print("   🔑 使用匿名密钥...")
        try:
            response = requests.get(url, headers=headers_anon, timeout=5)
            print(f"      状态码: {response.status_code}")
            if response.status_code < 500:
                print(f"      响应: {response.text[:200]}...")
            else:
                print(f"      错误: {response.text}")
        except Exception as e:
            print(f"      异常: {e}")
        
        # 使用服务角色密钥测试
        if service_role_key:
            print("   🔐 使用服务角色密钥...")
            try:
                response = requests.get(url, headers=headers_service, timeout=5)
                print(f"      状态码: {response.status_code}")
                if response.status_code < 500:
                    print(f"      响应: {response.text[:200]}...")
                else:
                    print(f"      错误: {response.text}")
            except Exception as e:
                print(f"      异常: {e}")


def test_supabase_client_auth():
    """测试 Supabase 客户端认证"""
    print("\n=== 🧪 Supabase 客户端认证测试 ===")
    
    try:
        client_service = get_supabase_client()
        client = client_service.get_client()
        
        print("✅ 客户端初始化成功")
        
        # 测试认证客户端的属性
        print(f"客户端 URL: {client.supabase_url}")
        print(f"客户端 Key: {client.supabase_key[:20]}...")
        
        # 尝试获取认证设置
        print("\n🔍 测试认证客户端...")
        try:
            # 直接访问认证客户端
            auth_client = client.auth
            print(f"认证客户端类型: {type(auth_client)}")
            
            # 尝试获取会话
            session = auth_client.get_session()
            print(f"当前会话: {session}")
            
        except Exception as e:
            print(f"认证客户端测试失败: {e}")
        
        # 测试管理员客户端
        print("\n🔐 测试管理员客户端...")
        try:
            admin_client = client_service.get_client(admin=True)
            if admin_client:
                print("✅ 管理员客户端获取成功")
                print(f"管理员客户端 Key: {admin_client.supabase_key[:20]}...")
            else:
                print("⚠️ 管理员客户端未配置")
        except Exception as e:
            print(f"管理员客户端测试失败: {e}")
            
    except Exception as e:
        print(f"❌ 客户端测试失败: {e}")


def test_auth_signup_debug():
    """调试认证注册过程"""
    print("\n=== 🔬 认证注册调试 ===")
    
    try:
        client_service = get_supabase_client()
        client = client_service.get_client()
        
        # 准备测试数据
        test_email = "<EMAIL>"
        test_password = "debug123456"
        
        print(f"测试邮箱: {test_email}")
        print(f"测试密码: {test_password}")
        
        # 尝试注册
        print("\n📝 尝试用户注册...")
        try:
            response = client.auth.sign_up({
                "email": test_email,
                "password": test_password
            })
            
            print(f"注册响应类型: {type(response)}")
            print(f"注册响应: {response}")
            
            if hasattr(response, 'user') and response.user:
                print(f"✅ 用户创建成功: {response.user.email}")
            else:
                print("⚠️ 注册响应中没有用户信息")
                
        except Exception as e:
            print(f"❌ 注册失败: {e}")
            print(f"异常类型: {type(e)}")
            
            # 尝试获取更详细的错误信息
            if hasattr(e, 'response'):
                print(f"HTTP 响应: {e.response}")
            if hasattr(e, 'status_code'):
                print(f"状态码: {e.status_code}")
                
    except Exception as e:
        print(f"❌ 调试测试失败: {e}")


def test_kong_routing():
    """测试 Kong 路由配置"""
    print("\n=== 🌉 Kong 路由测试 ===")
    
    base_url = supabase_settings.supabase_url
    
    # 测试不同的路由路径
    test_routes = [
        "/",
        "/health",
        "/auth/v1/",
        "/auth/v1/health",
        "/rest/v1/",
        "/storage/v1/"
    ]
    
    for route in test_routes:
        print(f"\n📍 测试路由: {route}")
        url = f"{base_url}{route}"
        
        try:
            response = requests.get(url, timeout=5)
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ 路由可用")
            elif response.status_code == 401:
                print(f"   🔑 需要认证（正常）")
            elif response.status_code == 404:
                print(f"   ❌ 路由不存在")
            elif response.status_code == 502:
                print(f"   ⚠️ 后端服务问题")
            else:
                print(f"   ❓ 其他状态")
                
            # 显示响应头
            if 'server' in response.headers:
                print(f"   服务器: {response.headers['server']}")
                
        except Exception as e:
            print(f"   ❌ 请求失败: {e}")


async def main():
    """主函数"""
    print("🔍 Supabase Auth 调试测试开始")
    print("=" * 50)
    
    # 1. 直接 API 调用测试
    test_direct_api_calls()
    
    # 2. Kong 路由测试
    test_kong_routing()
    
    # 3. Supabase 客户端测试
    test_supabase_client_auth()
    
    # 4. 认证注册调试
    test_auth_signup_debug()
    
    print("\n" + "=" * 50)
    print("🎯 调试测试完成")
    print("\n💡 建议:")
    print("   - 检查上面的错误信息")
    print("   - 确认 Supabase 服务配置")
    print("   - 验证 API 密钥是否正确")


if __name__ == "__main__":
    # 配置日志
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    
    asyncio.run(main())
